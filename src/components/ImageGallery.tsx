'use client';

import { useState, useEffect, useCallback } from 'react';
import ImageModal from './ImageModal';

interface ImageData {
  id: number;
  src: string;
  alt: string;
  title: string;
}

// Sample image data - replace with your actual image data source
const generateSampleImages = (count: number): ImageData[] => {
  const images: ImageData[] = [];
  for (let i = 1; i <= count; i++) {
    images.push({
      id: i,
      src: `https://picsum.photos/400/600?random=${i}`,
      alt: `Sample image ${i}`,
      title: `Image ${i}`,
    });
  }
  return images;
};

const IMAGES_PER_PAGE = 9; // 3 columns × 3 rows

export default function ImageGallery() {
  const [images, setImages] = useState<ImageData[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null);
  const [page, setPage] = useState(1);

  // Load initial images - only 3 rows (9 images)
  useEffect(() => {
    const initialImages = generateSampleImages(IMAGES_PER_PAGE);
    setImages(initialImages);
  }, []);

  // Load more images when scrolling - load 3 more rows each time
  const loadMoreImages = useCallback(() => {
    if (loading || !hasMore) return;

    setLoading(true);

    // Simulate API call delay
    setTimeout(() => {
      const newImages = generateSampleImages(IMAGES_PER_PAGE).map(img => ({
        ...img,
        id: img.id + (page * IMAGES_PER_PAGE),
        src: `https://picsum.photos/400/600?random=${img.id + (page * IMAGES_PER_PAGE)}`,
      }));

      setImages(prev => [...prev, ...newImages]);
      setPage(prev => prev + 1);
      setLoading(false);

      // Stop loading after 15 pages (135 images total) for demo
      if (page >= 15) {
        setHasMore(false);
      }
    }, 800);
  }, [loading, hasMore, page]);

  // Infinite scroll handler - trigger when near bottom
  useEffect(() => {
    const handleScroll = () => {
      if (
        window.innerHeight + document.documentElement.scrollTop
        >= document.documentElement.offsetHeight - 500
      ) {
        loadMoreImages();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [loadMoreImages]);

  const openModal = (image: ImageData) => {
    setSelectedImage(image);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  return (
    <div className="container mx-auto px-2 sm:px-4 py-8">
      <h1 className="text-2xl sm:text-3xl font-bold text-center mb-6 sm:mb-8">Image Gallery</h1>

      {/* Image Grid - Always 3 columns on all screen sizes */}
      <div className="grid grid-cols-3 gap-1 sm:gap-2 md:gap-4">
        {images.map((image) => (
          <div
            key={image.id}
            className="relative group cursor-pointer overflow-hidden rounded-sm sm:rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300"
            onClick={() => openModal(image)}
          >
            <div className="aspect-square bg-gray-200">
              <img
                src={image.src}
                alt={image.alt}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                loading="lazy"
              />
            </div>
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg
                  className="w-6 h-6 sm:w-8 sm:h-8 md:w-12 md:h-12 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"
                  />
                </svg>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Loading indicator */}
      {loading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* End of content message */}
      {!hasMore && (
        <div className="text-center py-8 text-gray-500">
          <p>You've reached the end of the gallery!</p>
        </div>
      )}

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal
          image={selectedImage}
          isOpen={!!selectedImage}
          onClose={closeModal}
        />
      )}
    </div>
  );
}
